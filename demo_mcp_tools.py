"""Demo script to test MCP tools without polling conflicts."""

import async<PERSON>
import json
from telegram_mcp_server import TelegramMCPServer
from config import config

async def demo_mcp_tools():
    """Demonstrate MCP tools functionality."""
    print("🤖 Telegram MCP Server Demo")
    print("=" * 50)
    
    # Create server instance
    server = TelegramMCPServer()
    
    try:
        # Initialize without starting polling
        print("1. Initializing server components...")
        from session_manager import session_manager
        await session_manager.start()
        
        # Test send_telegram_message tool
        print("\n2. Testing send_telegram_message tool...")
        result = await server._send_telegram_message({
            "user_id": config.DEFAULT_USER_ID,
            "message": "🚀 MCP Server Demo: This message was sent via MCP tool!"
        })
        print(f"Result: {result[0].text}")
        
        # Test start_chat_session tool
        print("\n3. Testing start_chat_session tool...")
        result = await server._start_chat_session({
            "user_id": config.DEFAULT_USER_ID,
            "greeting_message": "Hello! MCP Server is now active and ready for remote Claude control!"
        })
        print(f"Result: {result[0].text}")
        
        # Test get_server_status tool
        print("\n4. Testing get_server_status tool...")
        result = await server._get_server_status({})
        status = json.loads(result[0].text)
        print("Server Status:")
        print(f"  - Server Running: {status.get('server_running', False)}")
        print(f"  - Active Sessions: {status.get('active_sessions', 0)}")
        print(f"  - Bot Initialized: {status.get('telegram_bot_status', {}).get('bot_initialized', False)}")
        
        # Show session details if any
        if status.get('session_details'):
            print("  - Session Details:")
            for session in status['session_details']:
                print(f"    User {session['user_id']}: Active={session['is_active']}, Messages={session['message_count']}")
        
        print("\n5. Testing end_session tool...")
        result = await server._end_session({
            "user_id": config.DEFAULT_USER_ID,
            "farewell_message": "Demo completed! MCP tools are working correctly."
        })
        print(f"Result: {result[0].text}")
        
        print("\n" + "=" * 50)
        print("✅ All MCP tools are working correctly!")
        print("\nTo use with Claude:")
        print("1. Add this server to your Claude Desktop config")
        print("2. Say 'start new session' to Claude")
        print("3. Claude will call these MCP tools automatically")
        print("4. Check your Telegram for messages from @Mcpcodebot")
        
    except Exception as e:
        print(f"❌ Error during demo: {e}")
    
    finally:
        # Cleanup
        await session_manager.stop()

async def test_direct_telegram():
    """Test direct Telegram bot functionality."""
    print("\n" + "=" * 50)
    print("🔧 Testing Direct Telegram Bot")
    print("=" * 50)
    
    from telegram_bot import telegram_bot
    
    try:
        # Initialize bot without polling
        success = await telegram_bot.initialize()
        if success:
            print("✅ Bot initialized successfully")
            
            # Test sending a message
            print("📤 Sending test message...")
            success = await telegram_bot.send_message(
                config.DEFAULT_USER_ID,
                "🧪 Direct bot test: This confirms the bot can send messages!"
            )
            
            if success:
                print("✅ Message sent successfully!")
                print("Check your Telegram for the test message.")
            else:
                print("❌ Failed to send message")
        else:
            print("❌ Failed to initialize bot")
            
    except Exception as e:
        print(f"❌ Error testing bot: {e}")

if __name__ == "__main__":
    print("Starting MCP Server Demo...")
    asyncio.run(demo_mcp_tools())
    asyncio.run(test_direct_telegram())
