# 🤖 Telegram MCP Server for Remote Claude Control

This MCP (Model Context Protocol) server enables complete remote control of Claude AI agent through Telegram, providing persistent sessions and full tool access as described in the specification.

## Features

- **Remote Claude Control**: Control Claude AI agent from anywhere via Telegram
- **Persistent Sessions**: No session timeouts until explicitly ended
- **Context Preservation**: Maintains conversation history across interactions
- **Multi-User Support**: Handle multiple users with isolated sessions
- **Error Recovery**: Robust error handling and automatic reconnection
- **Real-time Communication**: Bidirectional message flow between Telegram and Claude

## Quick Start

### 1. Prerequisites

- Python 3.8+
- Telegram Bot Token (from @BotFather)
- <PERSON> AI with MCP support

### 2. Installation

```bash
# Clone or download the project
cd telegram-mcp-server

# Install dependencies
pip install -r requirements.txt

# Create logs directory
mkdir -p logs
```

### 3. Configuration

```bash
# Copy environment template
cp .env.template .env

# Edit .env with your settings
nano .env
```

Required configuration:
- `TELEGRAM_BOT_TOKEN`: Get from @BotFather on Telegram
- `DEFAULT_USER_ID`: Your Telegram user ID (default: **********)

### 4. Get Your Telegram User ID

Send a message to your bot, then check:
```bash
curl https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates
```

### 5. Run the Server

```bash
python main.py
```

## MCP Tools Available

### Core Tools

1. **start_chat_session**
   - Initialize new session with greeting message
   - Establishes persistent connection
   - Parameters: `user_id`, `greeting_message`

2. **send_telegram_message**
   - Send messages to Telegram users
   - Parameters: `user_id`, `message`

3. **get_telegram_updates**
   - Receive incoming messages from Telegram
   - Parameters: `user_id`, `wait_for_message`, `timeout`

4. **get_server_status**
   - Monitor system health and active sessions
   - Returns: server status, active sessions, bot info

5. **end_session**
   - Gracefully terminate user sessions
   - Parameters: `user_id`, `farewell_message`

## Usage with Claude

### Starting a Remote Session

In Claude, use the trigger phrase:
```
start new session
```

This will:
1. Connect to the Telegram MCP server
2. Send greeting to your Telegram account
3. Enter waiting state for your Telegram responses
4. Process your Telegram messages as direct prompts

### Example Workflow

```
Claude → Telegram: "Hello! I'm ready to assist you. What task would you like me to help with?"
You → Telegram: "Help me write a Python function for data processing"
Telegram → Claude: [Your message becomes Claude's prompt]
Claude: [Processes and generates Python function]
Claude → Telegram: [Function code + "Task completed! What's next?"]
```

### System Prompt Integration

Add this to your Claude configuration:

```
TELEGRAM MCP REMOTE CONTROL SYSTEM:

When the user says "start new session", immediately:
1. Connect to the Telegram MCP server using available tools
2. Send an initial greeting message to user ID **********
3. Enter persistent waiting mode for their Telegram response
4. Treat all incoming Telegram messages as direct prompts
5. After completing each task:
   - Send a summary of work completed
   - Ask for the next task
   - Return to waiting state
6. Maintain the connection until they explicitly end the session
```

## Architecture

### Components

- **TelegramMCPServer**: Main MCP server with tool definitions
- **TelegramBotHandler**: Telegram bot integration and message handling
- **SessionManager**: User session and context management
- **Config**: Configuration management with environment variables

### Session Management

- Individual sessions per user with context isolation
- Automatic cleanup of expired sessions
- Context history preservation (last 50 messages)
- Persistent waiting states for continuous interaction

### Error Handling

- Automatic reconnection on connection drops
- Graceful error messages sent to Telegram
- Comprehensive logging for debugging
- Rate limiting to prevent abuse

## Configuration Options

| Variable | Default | Description |
|----------|---------|-------------|
| `TELEGRAM_BOT_TOKEN` | Required | Bot token from @BotFather |
| `DEFAULT_USER_ID` | ********** | Default Telegram user ID |
| `SESSION_TIMEOUT` | 3600 | Session timeout in seconds |
| `MAX_CONTEXT_LENGTH` | 10000 | Maximum context length |
| `LOG_LEVEL` | INFO | Logging level |
| `MAX_MESSAGES_PER_MINUTE` | 30 | Rate limiting |

## Troubleshooting

### Common Issues

1. **Bot not responding**
   - Check bot token in .env
   - Verify bot is started with @BotFather
   - Check logs for connection errors

2. **Messages not received**
   - Verify user ID is correct
   - Check Telegram bot permissions
   - Review server logs

3. **Session timeouts**
   - Increase SESSION_TIMEOUT in .env
   - Check for network connectivity issues

### Logs

Check logs in:
- Console output (real-time)
- `logs/telegram_mcp_server.log` (persistent)

## Security

- User authentication via Telegram user IDs
- Message encryption through Telegram's security
- Session isolation between users
- Rate limiting to prevent abuse
- Audit logging of all interactions

## Development

### Project Structure

```
telegram-mcp-server/
├── main.py                 # Entry point
├── telegram_mcp_server.py  # Main MCP server
├── telegram_bot.py         # Telegram integration
├── session_manager.py      # Session management
├── config.py              # Configuration
├── requirements.txt       # Dependencies
├── .env.template         # Environment template
└── logs/                 # Log files
```

### Adding New Tools

1. Add tool definition in `_setup_tools()`
2. Implement handler method
3. Add to `call_tool()` dispatcher
4. Update documentation

## License

This project implements the Telegram MCP Remote Control specification for Claude AI agent integration.
