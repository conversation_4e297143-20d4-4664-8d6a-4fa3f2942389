"""Demo MCP client to simulate <PERSON> calling the tools."""

import asyncio
import json
from telegram_mcp_server import TelegramMCPServer
from config import config

class MCPClientDemo:
    """Simulates <PERSON> calling MCP tools."""
    
    def __init__(self):
        self.server = TelegramMCPServer()
    
    async def simulate_claude_workflow(self):
        """Simulate the complete <PERSON> workflow from the specification."""
        print("🤖 Simulating Claude AI Remote Control Workflow")
        print("=" * 60)
        
        try:
            # Initialize session manager
            from session_manager import session_manager
            await session_manager.start()
            
            print("📱 User says to <PERSON>: 'start new session'")
            print("🧠 <PERSON> responds by calling MCP tools...")
            print()
            
            # Step 1: <PERSON> calls start_chat_session
            print("1️⃣ <PERSON> calls: start_chat_session")
            result = await self.server._start_chat_session({
                "user_id": config.DEFAULT_USER_ID,
                "greeting_message": "Hello! I'm ready to assist you. What task would you like me to help with?"
            })
            print(f"   📤 {result[0].text}")
            print()
            
            # Step 2: Simulate user responding on Telegram
            print("2️⃣ User responds on Telegram: 'Help me write a Python function for data processing'")
            
            # Simulate message being received (normally this would come from Telegram)
            from session_manager import session_manager
            session = session_manager.get_session(config.DEFAULT_USER_ID)
            if session:
                session.add_message("Help me write a Python function for data processing", "user")
            
            # Step 3: Claude gets the message
            print("3️⃣ Claude calls: get_telegram_updates")
            # Simulate the message in the queue
            from telegram_bot import telegram_bot
            await telegram_bot.message_queue.put({
                "user_id": config.DEFAULT_USER_ID,
                "chat_id": config.DEFAULT_USER_ID,
                "text": "Help me write a Python function for data processing",
                "timestamp": 1234567890,
                "message_id": 123
            })
            
            result = await self.server._get_telegram_updates({
                "user_id": config.DEFAULT_USER_ID,
                "wait_for_message": False
            })
            updates = json.loads(result[0].text)
            print(f"   📥 Received {updates['count']} message(s)")
            if updates['messages']:
                print(f"   💬 Message: '{updates['messages'][0]['text']}'")
            print()
            
            # Step 4: Claude processes and responds
            print("4️⃣ Claude processes the request and sends response")
            python_function = '''def process_data(data):
    """Process data with filtering and transformation."""
    filtered_data = [item for item in data if item is not None]
    processed_data = [str(item).upper() for item in filtered_data]
    return processed_data

# Example usage:
# result = process_data(['hello', None, 'world', 123])
# Output: ['HELLO', 'WORLD', '123']'''
            
            result = await self.server._send_telegram_message({
                "user_id": config.DEFAULT_USER_ID,
                "message": f"Here's a Python function for data processing:\n\n```python\n{python_function}\n```\n\nTask completed! What would you like me to do next?"
            })
            print(f"   📤 {result[0].text}")
            print()
            
            # Step 5: Check server status
            print("5️⃣ Claude calls: get_server_status")
            result = await self.server._get_server_status({})
            status = json.loads(result[0].text)
            print(f"   📊 Active sessions: {status['active_sessions']}")
            print(f"   📊 Session messages: {status['session_details'][0]['message_count'] if status['session_details'] else 0}")
            print()
            
            # Step 6: Continue the conversation
            print("6️⃣ User continues: 'Now optimize that function for better performance'")
            
            # Add another message
            if session:
                session.add_message("Now optimize that function for better performance", "user")
            
            await telegram_bot.message_queue.put({
                "user_id": config.DEFAULT_USER_ID,
                "chat_id": config.DEFAULT_USER_ID,
                "text": "Now optimize that function for better performance",
                "timestamp": 1234567891,
                "message_id": 124
            })
            
            result = await self.server._get_telegram_updates({
                "user_id": config.DEFAULT_USER_ID,
                "wait_for_message": False
            })
            updates = json.loads(result[0].text)
            print(f"   📥 Received new message: '{updates['messages'][0]['text'] if updates['messages'] else 'None'}'")
            print()
            
            # Step 7: Claude sends optimized version
            print("7️⃣ Claude sends optimized function")
            optimized_function = '''def process_data_optimized(data):
    """Optimized data processing with list comprehension and type checking."""
    return [str(item).upper() for item in data if item is not None]

# Performance improvements:
# - Single list comprehension (faster)
# - Removed intermediate variable
# - More memory efficient'''
            
            result = await self.server._send_telegram_message({
                "user_id": config.DEFAULT_USER_ID,
                "message": f"Here's the optimized version:\n\n```python\n{optimized_function}\n```\n\nFunction optimized! Ready for your next request."
            })
            print(f"   📤 {result[0].text}")
            print()
            
            # Step 8: End session
            print("8️⃣ User ends session")
            result = await self.server._end_session({
                "user_id": config.DEFAULT_USER_ID,
                "farewell_message": "Session ended. Thank you for using the remote Claude control system!"
            })
            print(f"   👋 {result[0].text}")
            print()
            
            print("=" * 60)
            print("✅ Complete workflow simulation successful!")
            print()
            print("🎯 This demonstrates how Claude can be controlled remotely via Telegram:")
            print("   • User triggers with 'start new session'")
            print("   • Claude establishes persistent connection")
            print("   • Telegram messages become Claude prompts")
            print("   • Claude processes and responds via Telegram")
            print("   • Conversation continues until session ends")
            print()
            print("📱 Check your Telegram (@Mcpcodebot) for the actual messages!")
            
        except Exception as e:
            print(f"❌ Error in workflow: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await session_manager.stop()

async def main():
    """Run the demo."""
    demo = MCPClientDemo()
    await demo.simulate_claude_workflow()

if __name__ == "__main__":
    asyncio.run(main())
