"""Session management for Telegram MCP Server."""

import asyncio
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from loguru import logger

@dataclass
class UserSession:
    """Represents a user session with context and state."""
    user_id: int
    chat_id: int
    is_active: bool = True
    created_at: float = field(default_factory=time.time)
    last_activity: float = field(default_factory=time.time)
    context_history: List[Dict[str, Any]] = field(default_factory=list)
    waiting_for_response: bool = False
    current_task: Optional[str] = None

    def add_message(self, message: str, role: str = "user") -> None:
        """Add a message to the context history."""
        self.context_history.append({
            "role": role,
            "content": message,
            "timestamp": time.time()
        })
        self.last_activity = time.time()
        
        # Limit context history size
        if len(self.context_history) > 50:  # Keep last 50 messages
            self.context_history = self.context_history[-50:]

    def get_context_summary(self) -> str:
        """Get a summary of the conversation context."""
        if not self.context_history:
            return "No previous context."
        
        recent_messages = self.context_history[-10:]  # Last 10 messages
        summary = "Recent conversation:\n"
        for msg in recent_messages:
            role = msg["role"].capitalize()
            content = msg["content"][:100] + "..." if len(msg["content"]) > 100 else msg["content"]
            summary += f"{role}: {content}\n"
        
        return summary

    def is_expired(self, timeout: int) -> bool:
        """Check if session has expired."""
        return time.time() - self.last_activity > timeout


class SessionManager:
    """Manages user sessions and context."""
    
    def __init__(self):
        self.sessions: Dict[int, UserSession] = {}
        self._cleanup_task: Optional[asyncio.Task] = None
        
    async def start(self):
        """Start the session manager."""
        logger.info("Starting session manager")
        self._cleanup_task = asyncio.create_task(self._cleanup_expired_sessions())
    
    async def stop(self):
        """Stop the session manager."""
        logger.info("Stopping session manager")
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
    
    def create_session(self, user_id: int, chat_id: int) -> UserSession:
        """Create a new user session."""
        session = UserSession(user_id=user_id, chat_id=chat_id)
        self.sessions[user_id] = session
        logger.info(f"Created new session for user {user_id}")
        return session
    
    def get_session(self, user_id: int) -> Optional[UserSession]:
        """Get an existing session."""
        return self.sessions.get(user_id)
    
    def get_or_create_session(self, user_id: int, chat_id: int) -> UserSession:
        """Get existing session or create a new one."""
        session = self.get_session(user_id)
        if session is None or not session.is_active:
            session = self.create_session(user_id, chat_id)
        else:
            session.last_activity = time.time()
        return session
    
    def end_session(self, user_id: int) -> bool:
        """End a user session."""
        session = self.sessions.get(user_id)
        if session:
            session.is_active = False
            logger.info(f"Ended session for user {user_id}")
            return True
        return False
    
    def get_active_sessions(self) -> List[UserSession]:
        """Get all active sessions."""
        return [session for session in self.sessions.values() if session.is_active]
    
    async def _cleanup_expired_sessions(self):
        """Periodically cleanup expired sessions."""
        while True:
            try:
                await asyncio.sleep(300)  # Check every 5 minutes
                current_time = time.time()
                expired_users = []
                
                for user_id, session in self.sessions.items():
                    if session.is_expired(3600):  # 1 hour timeout
                        expired_users.append(user_id)
                
                for user_id in expired_users:
                    self.end_session(user_id)
                    logger.info(f"Cleaned up expired session for user {user_id}")
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in session cleanup: {e}")

# Global session manager instance
session_manager = SessionManager()
