"""Test script to verify Telegram MCP Server setup."""

import async<PERSON>
import os
from dotenv import load_dotenv
from telegram import <PERSON><PERSON>
from loguru import logger

async def test_telegram_bot():
    """Test Telegram bot connection."""
    load_dotenv()
    
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    if not bot_token:
        logger.error("TELEGRAM_BOT_TOKEN not found in .env file")
        return False
    
    try:
        bot = Bot(token=bot_token)
        bot_info = await bot.get_me()
        logger.info(f"Bot connected successfully: @{bot_info.username}")
        logger.info(f"Bot ID: {bot_info.id}")
        logger.info(f"Bot Name: {bot_info.first_name}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to connect to Telegram bot: {e}")
        return False

async def test_user_id():
    """Test if user ID is configured."""
    load_dotenv()
    
    user_id = os.getenv("DEFAULT_USER_ID")
    if not user_id:
        logger.warning("DEFAULT_USER_ID not set, using default: 5295836625")
        return True
    
    try:
        user_id_int = int(user_id)
        logger.info(f"User ID configured: {user_id_int}")
        return True
    except ValueError:
        logger.error(f"Invalid user ID format: {user_id}")
        return False

async def test_send_message():
    """Test sending a message to the configured user."""
    load_dotenv()
    
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    user_id = int(os.getenv("DEFAULT_USER_ID", "5295836625"))
    
    if not bot_token:
        logger.error("Cannot test message sending without bot token")
        return False
    
    try:
        bot = Bot(token=bot_token)
        await bot.send_message(
            chat_id=user_id,
            text="🤖 Telegram MCP Server test message - setup verification successful!"
        )
        logger.info(f"Test message sent successfully to user {user_id}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send test message: {e}")
        logger.info("This might be normal if the user hasn't started the bot yet")
        return False

async def main():
    """Run all tests."""
    logger.info("Starting Telegram MCP Server setup verification...")
    
    # Test 1: Environment file
    if not os.path.exists(".env"):
        logger.error(".env file not found. Copy .env.template to .env and configure it.")
        return
    
    logger.info("✓ .env file found")
    
    # Test 2: Bot connection
    bot_ok = await test_telegram_bot()
    if bot_ok:
        logger.info("✓ Telegram bot connection successful")
    else:
        logger.error("✗ Telegram bot connection failed")
        return
    
    # Test 3: User ID configuration
    user_ok = await test_user_id()
    if user_ok:
        logger.info("✓ User ID configuration valid")
    else:
        logger.error("✗ User ID configuration invalid")
        return
    
    # Test 4: Message sending (optional)
    logger.info("Testing message sending (this may fail if user hasn't started the bot)...")
    msg_ok = await test_send_message()
    if msg_ok:
        logger.info("✓ Test message sent successfully")
    else:
        logger.warning("⚠ Test message failed (user may need to start the bot first)")
    
    logger.info("\n" + "="*50)
    logger.info("Setup verification completed!")
    logger.info("To start the MCP server, run: python main.py")
    logger.info("="*50)

if __name__ == "__main__":
    asyncio.run(main())
