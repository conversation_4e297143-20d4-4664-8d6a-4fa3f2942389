"""Working demo that sends actual messages to Telegram."""

import asyncio
from telegram_mcp_server import TelegramMCPServer
from telegram_bot import telegram_bot
from session_manager import session_manager
from config import config

async def working_demo():
    """Demo that actually sends messages to Telegram."""
    print("🚀 Telegram MCP Server - Working Demo")
    print("=" * 50)
    
    try:
        # Initialize components
        print("1. Initializing components...")
        await session_manager.start()
        await telegram_bot.initialize()
        print("✅ Components initialized")
        
        # Create server instance
        server = TelegramMCPServer()
        
        print("\n2. Testing MCP Tools with actual Telegram messages...")
        
        # Test 1: Start chat session
        print("\n🔧 Testing start_chat_session...")
        result = await server._start_chat_session({
            "user_id": config.DEFAULT_USER_ID,
            "greeting_message": "🤖 Hello! I'm Claude AI, now remotely controlled via Telegram MCP! What task would you like me to help with?"
        })
        print(f"Result: {result[0].text}")
        
        # Wait a moment
        await asyncio.sleep(2)
        
        # Test 2: Send a task completion message
        print("\n🔧 Testing send_telegram_message...")
        task_response = """Here's a Python function for data processing:

```python
def process_data(data):
    \"\"\"Process data with filtering and transformation.\"\"\"
    filtered_data = [item for item in data if item is not None]
    processed_data = [str(item).upper() for item in filtered_data]
    return processed_data

# Example usage:
# result = process_data(['hello', None, 'world', 123])
# Output: ['HELLO', 'WORLD', '123']
```

✅ Task completed! What would you like me to do next?"""
        
        result = await server._send_telegram_message({
            "user_id": config.DEFAULT_USER_ID,
            "message": task_response
        })
        print(f"Result: {result[0].text}")
        
        # Wait a moment
        await asyncio.sleep(2)
        
        # Test 3: Send optimization
        print("\n🔧 Testing optimization response...")
        optimization_response = """Here's the optimized version:

```python
def process_data_optimized(data):
    \"\"\"Optimized data processing with list comprehension.\"\"\"
    return [str(item).upper() for item in data if item is not None]

# Performance improvements:
# - Single list comprehension (faster)
# - Removed intermediate variable  
# - More memory efficient
```

🚀 Function optimized! Ready for your next request."""
        
        result = await server._send_telegram_message({
            "user_id": config.DEFAULT_USER_ID,
            "message": optimization_response
        })
        print(f"Result: {result[0].text}")
        
        # Test 4: Get server status
        print("\n🔧 Testing get_server_status...")
        result = await server._get_server_status({})
        print("Server status retrieved successfully")
        
        # Test 5: End session
        print("\n🔧 Testing end_session...")
        result = await server._end_session({
            "user_id": config.DEFAULT_USER_ID,
            "farewell_message": "🎉 Demo completed! The Telegram MCP Server is working perfectly. You can now use 'start new session' with Claude to begin remote control!"
        })
        print(f"Result: {result[0].text}")
        
        print("\n" + "=" * 50)
        print("✅ ALL TESTS PASSED!")
        print("\n📱 Check your Telegram (@Mcpcodebot) for all the messages!")
        print("\n🎯 Next Steps:")
        print("1. Add this MCP server to Claude Desktop config")
        print("2. Say 'start new session' to Claude")
        print("3. Claude will automatically use these tools")
        print("4. Your Telegram becomes Claude's remote interface!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await session_manager.stop()

if __name__ == "__main__":
    asyncio.run(working_demo())
