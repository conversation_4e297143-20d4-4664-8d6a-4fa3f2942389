#!/bin/bash

# Telegram MCP Server Installation Script

echo "🤖 Installing Telegram MCP Server..."

# Check Python version
python_version=$(python3 --version 2>&1 | grep -oP '\d+\.\d+')
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python 3.8+ required. Found: $python_version"
    exit 1
fi

echo "✓ Python version check passed: $python_version"

# Create virtual environment
echo "📦 Creating virtual environment..."
python3 -m venv venv
source venv/bin/activate

# Install dependencies
echo "📥 Installing dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

# Create logs directory
echo "📁 Creating logs directory..."
mkdir -p logs

# Copy environment template
if [ ! -f ".env" ]; then
    echo "⚙️ Creating .env file from template..."
    cp .env.template .env
    echo "📝 Please edit .env file with your Telegram bot token"
else
    echo "✓ .env file already exists"
fi

echo ""
echo "🎉 Installation completed!"
echo ""
echo "Next steps:"
echo "1. Edit .env file with your Telegram bot token"
echo "2. Run: python test_setup.py (to verify setup)"
echo "3. Run: python main.py (to start the server)"
echo ""
echo "To activate the virtual environment:"
echo "source venv/bin/activate"
