"""Main entry point for Telegram MCP Server."""

import asyncio
import signal
import sys
from loguru import logger

from config import config
from telegram_mcp_server import TelegramMCPServer


class MCPServerRunner:
    """Runner for the MCP server with proper lifecycle management."""
    
    def __init__(self):
        self.server = TelegramMCPServer()
        self.shutdown_event = asyncio.Event()
    
    def setup_logging(self):
        """Setup logging configuration."""
        logger.remove()  # Remove default handler
        logger.add(
            sys.stderr,
            level=config.LOG_LEVEL,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
        )
        logger.add(
            "logs/telegram_mcp_server.log",
            rotation="1 day",
            retention="7 days",
            level=config.LOG_LEVEL,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"
        )
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            self.shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def run(self):
        """Run the MCP server."""
        try:
            self.setup_logging()
            self.setup_signal_handlers()
            
            logger.info("Starting Telegram MCP Server...")
            logger.info(f"Server name: {config.MCP_SERVER_NAME}")
            logger.info(f"Server version: {config.MCP_SERVER_VERSION}")
            logger.info(f"Default user ID: {config.DEFAULT_USER_ID}")
            
            # Start the server
            await self.server.start()
            
            # Run the MCP server
            from mcp.server.stdio import stdio_server
            
            async with stdio_server() as (read_stream, write_stream):
                await self.server.get_server().run(
                    read_stream,
                    write_stream,
                    self.server.get_server().create_initialization_options()
                )
            
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        except Exception as e:
            logger.error(f"Server error: {e}")
            raise
        finally:
            await self.cleanup()
    
    async def cleanup(self):
        """Cleanup resources."""
        try:
            logger.info("Cleaning up resources...")
            await self.server.stop()
            logger.info("Cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


async def main():
    """Main function."""
    runner = MCPServerRunner()
    await runner.run()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
