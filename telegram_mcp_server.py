"""Main MCP Server implementation for Telegram remote control."""

import asyncio
import json
from typing import Any, Dict, List, Optional
from mcp.server import Server
from mcp.types import Tool, TextContent
from loguru import logger

from config import config
from session_manager import session_manager
from telegram_bot import telegram_bot


class TelegramMCPServer:
    """MCP Server for Telegram remote control of Claude AI."""
    
    def __init__(self):
        self.server = Server(config.MCP_SERVER_NAME)
        self.is_running = False
        self._setup_tools()
    
    def _setup_tools(self):
        """Setup MCP tools for Telegram integration."""
        
        @self.server.list_tools()
        async def list_tools() -> List[Tool]:
            """List available tools."""
            return [
                Tool(
                    name="start_chat_session",
                    description="Initialize a new chat session with a Telegram user and send greeting message",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "user_id": {
                                "type": "integer",
                                "description": "Telegram user ID to start session with",
                                "default": config.DEFAULT_USER_ID
                            },
                            "greeting_message": {
                                "type": "string",
                                "description": "Initial greeting message to send",
                                "default": "Hello! I'm ready to assist you. What task would you like me to help with?"
                            }
                        }
                    }
                ),
                Tool(
                    name="send_telegram_message",
                    description="Send a message to a Telegram user",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "user_id": {
                                "type": "integer",
                                "description": "Telegram user ID to send message to"
                            },
                            "message": {
                                "type": "string",
                                "description": "Message content to send"
                            }
                        },
                        "required": ["user_id", "message"]
                    }
                ),
                Tool(
                    name="get_telegram_updates",
                    description="Get incoming messages from Telegram users",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "user_id": {
                                "type": "integer",
                                "description": "Optional user ID to filter messages for specific user"
                            },
                            "wait_for_message": {
                                "type": "boolean",
                                "description": "Whether to wait for new messages if none available",
                                "default": True
                            },
                            "timeout": {
                                "type": "integer",
                                "description": "Timeout in seconds for waiting",
                                "default": 30
                            }
                        }
                    }
                ),
                Tool(
                    name="get_server_status",
                    description="Get current server status and session information",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                ),
                Tool(
                    name="end_session",
                    description="End a chat session with a user",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "user_id": {
                                "type": "integer",
                                "description": "User ID to end session for"
                            },
                            "farewell_message": {
                                "type": "string",
                                "description": "Optional farewell message",
                                "default": "Session ended. Thank you for using the service!"
                            }
                        },
                        "required": ["user_id"]
                    }
                )
            ]
        
        @self.server.call_tool()
        async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool calls."""
            try:
                if name == "start_chat_session":
                    return await self._start_chat_session(arguments)
                elif name == "send_telegram_message":
                    return await self._send_telegram_message(arguments)
                elif name == "get_telegram_updates":
                    return await self._get_telegram_updates(arguments)
                elif name == "get_server_status":
                    return await self._get_server_status(arguments)
                elif name == "end_session":
                    return await self._end_session(arguments)
                else:
                    return [TextContent(type="text", text=f"Unknown tool: {name}")]
                    
            except Exception as e:
                logger.error(f"Error in tool call {name}: {e}")
                return [TextContent(type="text", text=f"Error: {str(e)}")]
    
    async def _start_chat_session(self, args: Dict[str, Any]) -> List[TextContent]:
        """Start a new chat session."""
        user_id = args.get("user_id", config.DEFAULT_USER_ID)
        greeting = args.get("greeting_message", "Hello! I'm ready to assist you. What task would you like me to help with?")
        
        try:
            # Create or get session
            session = session_manager.get_or_create_session(user_id, user_id)
            session.waiting_for_response = True
            
            # Send greeting message
            success = await telegram_bot.send_message(user_id, greeting)
            
            if success:
                session.add_message(greeting, "assistant")
                result = f"Chat session started with user {user_id}. Greeting message sent successfully. Now waiting for user response..."
                logger.info(result)
                return [TextContent(type="text", text=result)]
            else:
                return [TextContent(type="text", text=f"Failed to send greeting message to user {user_id}")]
                
        except Exception as e:
            logger.error(f"Error starting chat session: {e}")
            return [TextContent(type="text", text=f"Error starting session: {str(e)}")]
    
    async def _send_telegram_message(self, args: Dict[str, Any]) -> List[TextContent]:
        """Send a message to Telegram user."""
        user_id = args["user_id"]
        message = args["message"]
        
        try:
            success = await telegram_bot.send_message(user_id, message)
            
            if success:
                # Update session context
                session = session_manager.get_session(user_id)
                if session:
                    session.add_message(message, "assistant")
                
                return [TextContent(type="text", text=f"Message sent successfully to user {user_id}")]
            else:
                return [TextContent(type="text", text=f"Failed to send message to user {user_id}")]
                
        except Exception as e:
            logger.error(f"Error sending message: {e}")
            return [TextContent(type="text", text=f"Error sending message: {str(e)}")]

    async def _get_telegram_updates(self, args: Dict[str, Any]) -> List[TextContent]:
        """Get incoming messages from Telegram."""
        user_id = args.get("user_id")
        wait_for_message = args.get("wait_for_message", True)
        timeout = args.get("timeout", 30)

        try:
            messages = await telegram_bot.get_updates(user_id)

            if messages:
                # Process messages and update session context
                result_messages = []
                for msg in messages:
                    session = session_manager.get_session(msg["user_id"])
                    if session:
                        session.waiting_for_response = False
                        result_messages.append({
                            "user_id": msg["user_id"],
                            "text": msg["text"],
                            "timestamp": msg["timestamp"]
                        })

                return [TextContent(type="text", text=json.dumps({
                    "status": "success",
                    "messages": result_messages,
                    "count": len(result_messages)
                }, indent=2))]

            elif wait_for_message:
                # Wait for new messages
                start_time = asyncio.get_event_loop().time()
                while (asyncio.get_event_loop().time() - start_time) < timeout:
                    await asyncio.sleep(1)
                    messages = await telegram_bot.get_updates(user_id)
                    if messages:
                        result_messages = []
                        for msg in messages:
                            session = session_manager.get_session(msg["user_id"])
                            if session:
                                session.waiting_for_response = False
                                result_messages.append({
                                    "user_id": msg["user_id"],
                                    "text": msg["text"],
                                    "timestamp": msg["timestamp"]
                                })

                        return [TextContent(type="text", text=json.dumps({
                            "status": "success",
                            "messages": result_messages,
                            "count": len(result_messages)
                        }, indent=2))]

                return [TextContent(type="text", text=json.dumps({
                    "status": "timeout",
                    "messages": [],
                    "count": 0
                }, indent=2))]

            else:
                return [TextContent(type="text", text=json.dumps({
                    "status": "no_messages",
                    "messages": [],
                    "count": 0
                }, indent=2))]

        except Exception as e:
            logger.error(f"Error getting updates: {e}")
            return [TextContent(type="text", text=f"Error getting updates: {str(e)}")]

    async def _get_server_status(self, args: Dict[str, Any]) -> List[TextContent]:
        """Get server status and session information."""
        # args parameter is required by MCP interface but not used in this method
        try:
            active_sessions = session_manager.get_active_sessions()
            bot_info = telegram_bot.get_bot_info()

            status = {
                "server_running": self.is_running,
                "telegram_bot_status": bot_info,
                "active_sessions": len(active_sessions),
                "session_details": [
                    {
                        "user_id": session.user_id,
                        "is_active": session.is_active,
                        "waiting_for_response": session.waiting_for_response,
                        "last_activity": session.last_activity,
                        "message_count": len(session.context_history)
                    }
                    for session in active_sessions
                ]
            }

            return [TextContent(type="text", text=json.dumps(status, indent=2))]

        except Exception as e:
            logger.error(f"Error getting server status: {e}")
            return [TextContent(type="text", text=f"Error getting status: {str(e)}")]

    async def _end_session(self, args: Dict[str, Any]) -> List[TextContent]:
        """End a chat session."""
        user_id = args["user_id"]
        farewell_message = args.get("farewell_message", "Session ended. Thank you for using the service!")

        try:
            # Send farewell message
            await telegram_bot.send_message(user_id, farewell_message)

            # End session
            success = session_manager.end_session(user_id)

            if success:
                return [TextContent(type="text", text=f"Session ended successfully for user {user_id}")]
            else:
                return [TextContent(type="text", text=f"No active session found for user {user_id}")]

        except Exception as e:
            logger.error(f"Error ending session: {e}")
            return [TextContent(type="text", text=f"Error ending session: {str(e)}")]

    async def start(self):
        """Start the MCP server and Telegram bot."""
        try:
            logger.info("Starting Telegram MCP Server...")

            # Validate configuration
            config.validate()

            # Start session manager
            await session_manager.start()

            # Start Telegram bot
            await telegram_bot.start()

            self.is_running = True
            logger.info("Telegram MCP Server started successfully")

        except Exception as e:
            logger.error(f"Failed to start server: {e}")
            raise

    async def stop(self):
        """Stop the MCP server and cleanup."""
        try:
            logger.info("Stopping Telegram MCP Server...")

            self.is_running = False

            # Stop Telegram bot
            await telegram_bot.stop()

            # Stop session manager
            await session_manager.stop()

            logger.info("Telegram MCP Server stopped successfully")

        except Exception as e:
            logger.error(f"Error stopping server: {e}")

    def get_server(self) -> Server:
        """Get the MCP server instance."""
        return self.server
