"""Telegram bot integration for MCP Server."""

import asyncio
from typing import Optional, List, Dict, Any
from telegram import Bot, Update
from telegram.ext import Application, MessageHandler, filters, ContextTypes
from loguru import logger

from config import config
from session_manager import session_manager, UserSession


class TelegramBotHandler:
    """Handles Telegram bot operations."""
    
    def __init__(self):
        self.bot: Optional[Bot] = None
        self.application: Optional[Application] = None
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self.is_running = False
        
    async def initialize(self):
        """Initialize the Telegram bot."""
        try:
            self.application = Application.builder().token(config.TELEGRAM_BOT_TOKEN).build()
            self.bot = self.application.bot
            
            # Add message handler
            message_handler = MessageHandler(filters.TEXT & ~filters.COMMAND, self._handle_message)
            self.application.add_handler(message_handler)
            
            logger.info("Telegram bot initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Telegram bot: {e}")
            return False
    
    async def start(self):
        """Start the Telegram bot."""
        if not self.application:
            await self.initialize()
        
        try:
            await self.application.initialize()
            await self.application.start()
            await self.application.updater.start_polling()
            self.is_running = True
            logger.info("Telegram bot started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start Telegram bot: {e}")
            raise
    
    async def stop(self):
        """Stop the Telegram bot."""
        if self.application and self.is_running:
            try:
                await self.application.updater.stop()
                await self.application.stop()
                await self.application.shutdown()
                self.is_running = False
                logger.info("Telegram bot stopped successfully")
                
            except Exception as e:
                logger.error(f"Error stopping Telegram bot: {e}")
    
    async def send_message(self, user_id: int, message: str) -> bool:
        """Send a message to a Telegram user."""
        if not self.bot:
            logger.error("Bot not initialized")
            return False
        
        try:
            await self.bot.send_message(chat_id=user_id, text=message)
            logger.info(f"Message sent to user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send message to user {user_id}: {e}")
            return False
    
    async def get_updates(self, user_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get recent messages from the queue."""
        messages = []
        try:
            # Get all available messages from queue (non-blocking)
            while not self.message_queue.empty():
                try:
                    message = self.message_queue.get_nowait()
                    if user_id is None or message.get("user_id") == user_id:
                        messages.append(message)
                except asyncio.QueueEmpty:
                    break
                    
        except Exception as e:
            logger.error(f"Error getting updates: {e}")
        
        return messages
    
    async def _handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle incoming Telegram messages."""
        try:
            if not update.message or not update.message.text:
                return
            
            user_id = update.effective_user.id
            chat_id = update.effective_chat.id
            message_text = update.message.text
            
            logger.info(f"Received message from user {user_id}: {message_text[:50]}...")
            
            # Get or create session
            session = session_manager.get_or_create_session(user_id, chat_id)
            session.add_message(message_text, "user")
            
            # Add message to queue for MCP server processing
            message_data = {
                "user_id": user_id,
                "chat_id": chat_id,
                "text": message_text,
                "timestamp": update.message.date.timestamp(),
                "message_id": update.message.message_id
            }
            
            await self.message_queue.put(message_data)
            logger.debug(f"Message queued for processing: {message_data}")
            
        except Exception as e:
            logger.error(f"Error handling message: {e}")
    
    def get_bot_info(self) -> Dict[str, Any]:
        """Get bot information and status."""
        return {
            "is_running": self.is_running,
            "bot_initialized": self.bot is not None,
            "queue_size": self.message_queue.qsize(),
            "active_sessions": len(session_manager.get_active_sessions())
        }

# Global bot handler instance
telegram_bot = TelegramBotHandler()
